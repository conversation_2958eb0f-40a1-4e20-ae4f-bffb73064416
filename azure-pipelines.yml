trigger:
  branches:
    include:
      - main  # Change this to your branch name if needed

pool:
  vmImage: 'ubuntu-latest'
        
steps:
- task: AzureCLI@2
  inputs:
    azureSubscription: 'cdss3_uat_resource_group'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      storageAccountName='ctintcdss3uatakssa'
      fileShareName='ctint-cdss-globalconfig'
      localFilePath='./config/uat/ctint-global-config-uat.yaml'
      storageAccountKey=$(StorageAccountKey)  # Reference the secret variable

      az storage file upload \
        --account-name $storageAccountName \
        --share-name $fileShareName \
        --source $localFilePath \
        --path $(basename $localFilePath) \
        --account-key $storageAccountKey  # Include the access key here
   