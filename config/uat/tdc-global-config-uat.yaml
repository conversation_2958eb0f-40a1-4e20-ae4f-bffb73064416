auth:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
logger:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: apne2.pure.cloud
  apiHost: https://api.apne2.pure.cloud
  authHost: https://login.apne2.pure.cloud
loginTotal: 1
missingTotal: 3
poolTotal: 10000
heartbeatTime: 30
socketType: CDSS
recording:
  mediaSource:
  - aws
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  user:
    host: http://ctint-dab-user-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  dataSync:
    host: http://ctint-dab-datasync-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true  
  callControl:
    host: http://ctint-dab-call-control-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  jobWorker:
    host: http://ctint-dab-job-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  common:
    host: http://ctint-graphql-service.ctint-tdc.svc.cluster.local:8110 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /tdc/query
    active: true
  auditLog:
    host: http://ctint-dab-audit-log-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  qm:
    host: http://ctint-dab-qm-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  message:
    host : http://ctint-dab-message-service.ctint-tdc.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  manualqueue:
    host: http://ctint-dab-manualqueue-service.ctint-tdc.svc.cluster.local:5000
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.ctint-tdc.svc.cluster.local:35000
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.ctint-tdc.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
  session-manager:
    host: http://ctint-state-session-manager-service.ctint-tdc.svc.cluster.local:35020
    basepath: /v1.0/state/ctint-state-session-manager
    active: true
  datasync:
    host: http://ctint-state-datasync-service.ctint-tdc.svc.cluster.local:35030
    basepath: /v1.0/state/ctint-state-datasync
    active: true
portals:
- ctint-mf-cpp
services:
  ctint-stt:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/stt
    active: false
    provider: 
    - astri1.5
    healthcheck: /healthcheck
  ctint-nlp:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/nlp
    active: false
    provider: 
    - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/auth
    active: true
    provider: # pure_engage / genesys_cloud / ctint-dab-auth / ctint-state-auth
    - genesys-cloud
    - ctint-dab-auth
    - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/conv
    active: true
    provider: 
    - pureengage
    - ctint-dab-conv
    healthcheck: /healthcheck
    criteriaSearch:
    - labelEn: Type
      labelCh: 媒體類型
      value: mediaType
      filterType: select
      active: true
      isMetaData: false
    - labelEn: Users
      labelCh: 用戶資訊
      value: users
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Start Time
      labelCh: 開始時間
      value: conversationStart
      filterType: date
      active: true
      isMetaData: false
    - labelEn: End Time
      labelCh: 結束時間
      value: conversationEnd
      filterType: date
      active: true
      isMetaData: false
    - labelEn: Conversation ID
      labelCh: 對話 ID
      value: conversationId
      filterType: input
      active: false
      isMetaData: false
    - labelEn: ANI
      labelCh: 來電號碼
      value: ani
      filterType: input
      active: false
      isMetaData: false            
    - labelEn: DNIS
      labelCh: 撥號號碼
      value: dnis
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Duration
      labelCh: 持續時間
      value: conversationDuration
      filterType: compare
      active: true
      isMetaData: false
    - labelEn: Queues
      labelCh: 队列
      value: queues
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Media Source
      labelCh: 媒體來源
      value: recordingMediaType
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Customer Remote
      labelCh: 客户远程
      value: customerRemote
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Wrapups
      labelCh: 总结代码
      value: wrapups
      filterType: input
      active: true
      isMetaData: false 
    - labelEn: Provider
      labelCh: 提供者
      value: provider
      filterType: input
      active: false
      isMetaData: false 
    - labelEn: Recording
      labelCh: 是否录音
      value: recording
      filterType: bool
      active: false
      isMetaData: false
    - labelEn: Direction
      labelCh: 方向
      value: direction
      filterType: select
      active: true
      isMetaData: false 
    - labelEn: FinalResult
      labelCh: 最终结果
      value: finalResult
      filterType: select
      active: true
      isMetaData: false
    - labelEn: accountId
      labelCh: 账号Id
      value: accountId
      filterType: input
      active: false
      isMetaData: true
    - labelEn: cif
      labelCh: cif
      value: cif
      filterType: input
      active: true
      isMetaData: true
  ctint-config:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/config
    active: true
    provider: 
    - pureengage
    - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/session
    active: true
    provider: 
    - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-session-manager:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/session-manager
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-ccsp-ws:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/ccsp-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/user
    active: true
    healthcheck: /healthcheck
  ctint-datasync-hub:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/datasync-hub
    active: true
    healthcheck: /healthcheck
  ctint-job-worker:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/job-worker
    active: true
    healthcheck: /healthcheck
  ctint-job-worker-auto-qm:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/job-worker-auto-qm
    active: true
    healthcheck: /healthcheck
  ctint-job-engine:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/job-engine
    active: true
    healthcheck: /healthcheck
  ctint-qm:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/qm
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-audit-log:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/audit-log
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-ocr:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/ocr
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-manual-queue:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/cdss-manual-queue
    active: true
    healthcheck: /healthcheck
    queueIds:
    - 580a0794-0940-4e99-9aef-bba93c142ac2
    callBackIds:
    - 580a0794-0940-4e99-9aef-bba93c142ac2
microfrontends:
  gc-client-id: f06b3c7f-1e25-45d8-b3b9-d9854f2ed32a
  gc-auth-url: https://login.apne2.pure.cloud/oauth/authorize
  gc-webrtc-iframe-url: https://apps.apne2.pure.cloud/crm/embeddableFramework.html?enableFrameworkClientId=true
  tenant: tdc
  ctint-mf-cdss:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/mf-cdss
    manual-queue:
    - queueName: Callback
      queueId: 580a0794-0940-4e99-9aef-bba93c142ac2
      type: callback
    user-tab-names:
    - labelEn: Name
      labelCh: 姓名
      value: name
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
    - labelEn: Username
      labelCh: 用戶賬號
      value: email
      filterType: input
      readOnly: false
      active: true
      require: true
    - labelEn: State
      labelCh: 狀態
      value: state
      filterType: select
      readOnly: false
      active: true
      require: false
    - labelEn: Login Type
      labelCh: 登录方式
      value: authtype
      filterType: select
      readOnly: true
      active: true
      require: false
    - labelEn: Description
      labelCh: 描述
      value: description
      filterType: input
      readOnly: false
      active: true
      require: false
    - labelEn: User Groups
      labelCh: 用戶組
      value: groupNames
      filterType: multipleSelect
      readOnly: false
      active: true
      require: false
    - labelEn: Roles
      labelCh: 角色
      value: roleNames
      filterType: multipleSelect
      readOnly: false
      active: true
      require: false
    - labelEn: Division
      labelCh: 部門
      value: divisionName
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Platform
      labelCh: 平台
      value: platform
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Tenant
      labelCh: 租戶
      value: tenant
      readOnly: true
      filterType: input
      active: true
      require: false
    - labelEn: Created By
      labelCh: 創建用戶
      value: createBy
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Create At
      labelCh: 創建時間
      value: createTime
      filterType: dateRange
      readOnly: true
      active: true
      require: false
    - labelEn: Update By
      labelCh: 更新用戶
      value: updateBy
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Update At
      labelCh: 更新時間
      value: updateTime
      filterType: dateRange
      readOnly: true
      active: true
      require: false
    audit-tab-names: 
    - labelEn: Event Type
      labelCh: 事件類型
      value: eventType
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: Event Time
      labelCh: 事件時間
      value: eventTimestamp
      filterType: dateRange
      readOnly: false
      active: true
      sort: true
    - labelEn: IP Address
      labelCh: IP地址
      value: ipAddress
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: User Agent
      labelCh: 用戶代理
      value: userAgent
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Browser
      labelCh: 瀏覽器
      value: browser
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: Operating System
      labelCh: 操作系統
      value: operatingSystem
      readOnly: true
      filterType: input
      active: true
      sort: false
    - labelEn: Device
      labelCh: 設備
      value: device
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Failure Reason
      labelCh: 失敗原因
      value: failureReason
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Additional Info
      labelCh: 附加信息
      value: additionalInfo
      filterType: input
      readOnly: true
      active: true
      sort: false
  ctint-mf-interaction:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/mf-interaction
  ctint-mf-manual-queue:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/mf-manual-queue
    manual-tab-names:
      - labelEn: Conversation ID
        labelCh: 對話 ID
        value: conversationId
        filterType: input
        active: false
        isMetaData: false
      - labelEn: Type
        labelCh: 媒體類型
        value: mediaType
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: State
        labelCh: 狀態
        value: state
        active: true
        require: false
        isMetaData: false
      - labelEn: From
        labelCh: 來自
        value: from
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Current Assignee
        labelCh: 目前處理者
        value: currentAgent
        active: true
      - labelEn: Assignment Records
        labelCh: 委派記錄
        value: assign
        filterType: select
        active: true
        isMetaData: false
      - labelEn: CallBack Number
        labelCh: 回撥號碼
        value: callbackNumber
        filterType: input
        active: true
        isMetaData: false
      - labelEn: QueueName
        labelCh: 佇列
        value: queueName
        filterType: select
        active: true
        isMetaData: true
      - labelEn: Created At
        labelCh: 開始時間
        value: createdAt
        filterType: dateRange
        active: true
        isMetaData: false
        checked: false
        require: true
      - labelEn: ConversationDuration
        labelCh: 對話持續期間
        value: conversationDuration
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Ended
        labelCh: 已結束
        value: ended
        filterType: select
        active: true
        isMetaData: false
  ctint-mf-super-dashboard:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/mf-super-dashboard
  ctint-mf-tdc:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/mf-tdc
  ctint-mf-call:
    host: https://ctint-cdss3-project-uat-aks.eastasia.cloudapp.azure.com
    basepath: /tdc/mf-call
languages:
  supportedLanguages:
  - en
  - zh-HK
  defaultLanguage: en
settings:
  defaultStorage: aws
aws:
  default:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
  stt:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
  ocr:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
permissionRelease: true


redis:
  url: digital.redis.cache.windows.net
  port: 6379
  password: aLOiX6vK2idUSzVdYxNvcWAYT5sMrX6bmAzCaDOvF5I=
  ctint-manual-queue:
    index: 0
  ctint-message:
    index: 1